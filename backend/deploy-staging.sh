#!/bin/bash

# GameFlex Backend - Staging Deployment Script
# This script deploys the SAM application to the staging environment

set -e  # Exit on any error

echo "🚀 Starting GameFlex Backend Staging Deployment..."

# Set environment to staging
export ENVIRONMENT=staging
export CERTIFICATE_ARN="arn:aws:acm:us-west-2:405316604661:certificate/54a5ec1c-0b64-4049-8969-6eff7e0d6bd8"

# Check if staging samconfig exists
if [ ! -f "samconfig-staging.toml" ]; then
    echo "❌ samconfig-staging.toml not found"
    echo "💡 Create it from the example or copy from samconfig.toml.example"
    echo "   Make sure it has proper staging configuration"
    exit 1
fi

echo "📋 Using staging configuration from samconfig-staging.toml..."

# Check if required environment variables are set
if [ -z "$CERTIFICATE_ARN" ]; then
    echo "❌ Error: CERTIFICATE_ARN environment variable is required for staging deployment"
    echo "   Please set it to your ACM certificate ARN for *.gameflex.io"
    echo "   Example: export CERTIFICATE_ARN='arn:aws:acm:us-west-2:123456789012:certificate/12345678-1234-1234-1234-123456789012'"
    exit 1
fi

# Optional: Check if AWS credentials are configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ Error: AWS credentials not configured or invalid"
    echo "   Please run 'aws configure' or set AWS environment variables"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Build the SAM application
echo "🔨 Building SAM application..."
sam build --config-file samconfig-staging.toml

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build completed successfully"

# Deploy to staging
echo "🚀 Deploying to staging environment..."
echo "   - Stack: gameflex-staging"
echo "   - Environment: staging"
echo "   - Domain: staging.api.gameflex.io"
echo "   - Media Domain: staging.media.gameflex.io"
echo "   - Certificate: $CERTIFICATE_ARN"
echo ""
echo "📋 SAM Configuration:"
echo "   - Config File: samconfig-staging.toml"
echo "   - Parameter Overrides: CertificateArn=$CERTIFICATE_ARN"
echo ""

# Deploy to staging
sam deploy --config-file samconfig-staging.toml \
    --parameter-overrides \
        "CertificateArn=$CERTIFICATE_ARN"

if [ $? -ne 0 ]; then
    echo "❌ Deployment failed"
    exit 1
fi

echo "✅ Deployment completed successfully!"

# Get the outputs
echo "📋 Deployment Information:"
echo "=========================="

# Get stack outputs
STACK_NAME="gameflex-staging"
API_URL=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

USER_POOL_ID=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`UserPoolId`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

USER_POOL_CLIENT_ID=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`UserPoolClientId`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

R2_SECRET_NAME=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`R2SecretName`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

echo "API Gateway URL: $API_URL"
echo "User Pool ID: $USER_POOL_ID"
echo "User Pool Client ID: $USER_POOL_CLIENT_ID"
echo "R2 Secret Name: $R2_SECRET_NAME"
echo ""

# Update samconfig.toml with deployment outputs
echo "📝 Updating samconfig.toml with deployment outputs..."
if [ -f "samconfig.toml" ]; then
    # Create a backup
    cp samconfig.toml samconfig.toml.backup

    # Update the values in samconfig.toml staging section
    if [ "$USER_POOL_ID" != "Not available" ]; then
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s/^USER_POOL_ID=.*/USER_POOL_ID=$USER_POOL_ID/" samconfig.toml
    fi

    if [ "$USER_POOL_CLIENT_ID" != "Not available" ]; then
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s/^USER_POOL_CLIENT_ID=.*/USER_POOL_CLIENT_ID=$USER_POOL_CLIENT_ID/" samconfig.toml
    fi

    if [ "$R2_SECRET_NAME" != "Not available" ]; then
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s/^R2_SECRET_NAME=.*/R2_SECRET_NAME=$R2_SECRET_NAME/" samconfig.toml
    fi

    # Update API URLs if available
    if [ "$API_URL" != "Not available" ]; then
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s|^API_URL_REMOTE=.*|API_URL_REMOTE=$API_URL|" samconfig.toml
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s|^API_BASE_URL=.*|API_BASE_URL=$API_URL|" samconfig.toml
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s|^TEST_API_BASE_URL=.*|TEST_API_BASE_URL=$API_URL|" samconfig.toml
    fi

    echo "✅ Updated samconfig.toml with deployment outputs"
    echo "   Backup saved as samconfig.toml.backup"
else
    echo "⚠️  samconfig.toml file not found, skipping update"
fi

# Instructions for next steps
echo "🔧 Next Steps:"
echo "=============="
echo "1. Configure your DNS to point staging.api.gameflex.io to the API Gateway"
echo "2. Configure your DNS to point staging.media.gameflex.io to your CloudFlare R2 bucket"
echo "3. Update the R2 secret in AWS Secrets Manager with your CloudFlare R2 credentials:"
echo "   aws secretsmanager put-secret-value \\"
echo "     --secret-id '$R2_SECRET_NAME' \\"
echo "     --secret-string '{\"accountId\":\"YOUR_R2_ACCOUNT_ID\",\"accessKeyId\":\"YOUR_R2_ACCESS_KEY\",\"secretAccessKey\":\"YOUR_R2_SECRET_KEY\",\"endpoint\":\"https://YOUR_ACCOUNT_ID.r2.cloudflarestorage.com\",\"bucketName\":\"gameflex-staging\",\"publicUrl\":\"https://staging.media.gameflex.io\"}'"
echo ""
echo "4. Test the deployment:"
echo "   curl $API_URL/health"
echo ""
echo "🎉 Staging deployment complete!"

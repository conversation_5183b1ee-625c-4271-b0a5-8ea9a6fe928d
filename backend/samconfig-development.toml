version = 0.1

# Development Environment Configuration
[default.deploy.parameters]
stack_name = "gameflex-development"
resolve_s3 = true
s3_prefix = "gameflex-development"
region = "us-west-2"
confirm_changeset = true
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=\"development\" ProjectName=\"gameflex\" R2AccountId=\"\" R2AccessKeyId=\"\" R2SecretAccessKey=\"\" R2Endpoint=\"\" R2BucketName=\"gameflex-development\" R2PublicUrl=\"https://pub-34709f09e8384ef1a67928492571c01d.r2.dev\""
image_repositories = []

[default.build.parameters]
cached = true
parallel = true

# Development Environment Variables
[default.global.parameters]
environment_variables = """
ENVIRONMENT=development
PROJECT_NAME=gameflex
AWS_REGION=us-west-2
API_PORT=3000
API_URL_REMOTE=https://go1iax5b8f.execute-api.us-west-2.amazonaws.com/v1
API_BASE_URL=https://go1iax5b8f.execute-api.us-west-2.amazonaws.com/v1
DEBUG=1
USER_POOL_ID=us-west-2_jZQ65BOBt
USER_POOL_CLIENT_ID=4vk16jkh0cnh4r2ebg2ltfvbfg
USERS_TABLE=gameflex-development-Users
POSTS_TABLE=gameflex-development-Posts
MEDIA_TABLE=gameflex-development-Media
USER_PROFILES_TABLE=gameflex-development-UserProfiles
COMMENTS_TABLE=gameflex-development-Comments
LIKES_TABLE=gameflex-development-Likes
FOLLOWS_TABLE=gameflex-development-Follows
CHANNELS_TABLE=gameflex-development-Channels
CHANNEL_MEMBERS_TABLE=gameflex-development-ChannelMembers
REFLEXES_TABLE=gameflex-development-Reflexes
R2_SECRET_NAME=gameflex-r2-config-development
APP_CONFIG_SECRET_NAME=gameflex-app-config-development
"""

[default.local_start_api.parameters]
warm_containers = "EAGER"
skip_pull_image = true
docker_network = "sam-local"

[default.local_start_lambda.parameters]
warm_containers = "EAGER"

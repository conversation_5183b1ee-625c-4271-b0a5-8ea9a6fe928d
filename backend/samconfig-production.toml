version = 0.1

# Production Environment Configuration
[default.deploy.parameters]
stack_name = "gameflex-production"
resolve_s3 = true
s3_prefix = "gameflex-production"
region = "us-west-2"
confirm_changeset = false
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=\"production\" ProjectName=\"gameflex\" DomainName=\"api.gameflex.io\" CertificateArn=\"\" R2BucketName=\"gameflex-production\" R2PublicUrl=\"https://media.gameflex.io\""
image_repositories = []

[default.build.parameters]
cached = true
parallel = true

# Production Environment Variables
[default.global.parameters]
environment_variables = """
ENVIRONMENT=production
PROJECT_NAME=gameflex
AWS_REGION=us-west-2
API_PORT=3000
API_URL_REMOTE=https://api.gameflex.io
API_BASE_URL=https://api.gameflex.io
DEBUG=0
USER_POOL_ID=
USER_POOL_CLIENT_ID=
USERS_TABLE=gameflex-production-Users
POSTS_TABLE=gameflex-production-Posts
MEDIA_TABLE=gameflex-production-Media
USER_PROFILES_TABLE=gameflex-production-UserProfiles
COMMENTS_TABLE=gameflex-production-Comments
LIKES_TABLE=gameflex-production-Likes
FOLLOWS_TABLE=gameflex-production-Follows
CHANNELS_TABLE=gameflex-production-Channels
CHANNEL_MEMBERS_TABLE=gameflex-production-ChannelMembers
REFLEXES_TABLE=gameflex-production-Reflexes
R2_SECRET_NAME=gameflex-r2-config-production
APP_CONFIG_SECRET_NAME=gameflex-app-config-production
DOMAIN_NAME=api.gameflex.io
MEDIA_DOMAIN=media.gameflex.io
SSL_ENABLED=true
FORCE_HTTPS=true
"""

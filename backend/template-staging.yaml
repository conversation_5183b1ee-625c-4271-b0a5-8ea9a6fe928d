AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: GameFlex Backend - SAM Template for Staging Environment

Parameters:
  CertificateArn:
    Type: String
    Default: ""
    Description: ACM Certificate ARN for custom domain (required for staging)

  R2AccountId:
    Type: String
    Default: ""
    Description: CloudFlare R2 Account ID (optional)

  R2AccessKeyId:
    Type: String
    Default: ""
    Description: CloudFlare R2 Access Key ID (optional)

  R2SecretAccessKey:
    Type: String
    Default: ""
    Description: CloudFlare R2 Secret Access Key (optional)

  R2Endpoint:
    Type: String
    Default: ""
    Description: CloudFlare R2 Endpoint URL (optional)

Globals:
  Function:
    Runtime: nodejs20.x
    Timeout: 30
    MemorySize: 256
    Environment:
      Variables:
        ENVIRONMENT: staging
        PROJECT_NAME: gameflex
        USER_POOL_ID: !Ref UserPool
        USER_POOL_CLIENT_ID: !Ref UserPoolClient
        POSTS_TABLE: !Ref PostsTable
        MEDIA_TABLE: !Ref MediaTable
        USER_PROFILES_TABLE: !Ref UserProfilesTable
        COMMENTS_TABLE: !Ref CommentsTable
        LIKES_TABLE: !Ref LikesTable
        FOLLOWS_TABLE: !Ref FollowsTable
        CHANNELS_TABLE: !Ref ChannelsTable
        CHANNEL_MEMBERS_TABLE: !Ref ChannelMembersTable
        REFLEXES_TABLE: !Ref ReflexesTable
        USERS_TABLE: !Ref UsersTable
        # AWS Secrets Manager Configuration
        R2_SECRET_NAME: !Ref R2Secret
        APP_CONFIG_SECRET_NAME: !Ref AppConfigSecret
        # Legacy environment variables (for backward compatibility)
        R2_ACCOUNT_ID: !Ref R2AccountId
        R2_ACCESS_KEY_ID: !Ref R2AccessKeyId
        R2_SECRET_ACCESS_KEY: !Ref R2SecretAccessKey
        R2_ENDPOINT: !Ref R2Endpoint
        R2_BUCKET_NAME: gameflex-staging
        R2_PUBLIC_URL: "https://staging.media.gameflex.io"

Conditions:
  HasCustomDomain: !Not [!Equals [!Ref CertificateArn, ""]]

Resources:
  # AWS Secrets Manager for R2 Configuration
  R2Secret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: gameflex-r2-config-staging
      Description: CloudFlare R2 configuration for GameFlex Staging
      SecretString: !Sub |
        {
          "accountId": "${R2AccountId}",
          "accessKeyId": "${R2AccessKeyId}",
          "secretAccessKey": "${R2SecretAccessKey}",
          "endpoint": "${R2Endpoint}",
          "bucketName": "gameflex-staging",
          "publicUrl": "https://staging.media.gameflex.io"
        }
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  # AWS Secrets Manager for General Configuration
  AppConfigSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: gameflex-app-config-staging
      Description: General application configuration for GameFlex Staging
      SecretString: !Sub |
        {
          "cloudflareApiToken": "",
          "testUserEmail": "<EMAIL>",
          "testUserPassword": "Test123!",
          "debugMode": "staging",
          "apiBaseUrl": "",
          "userPoolId": "",
          "userPoolClientId": ""
        }
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  # Custom Domain for API Gateway
  ApiDomainName:
    Type: AWS::ApiGateway::DomainName
    Condition: HasCustomDomain
    Properties:
      DomainName: staging.api.gameflex.io
      CertificateArn: !Ref CertificateArn
      SecurityPolicy: TLS_1_2

  # API Gateway with Lambda Authorizer
  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      StageName: v1
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
      Auth:
        Authorizers:
          CognitoAuthorizer:
            FunctionArn: !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:gameflex-authorizer-staging"
            FunctionPayloadType: TOKEN
            Identity:
              Header: Authorization

  # Base Path Mapping for Custom Domain
  ApiBasePathMapping:
    Type: AWS::ApiGateway::BasePathMapping
    Condition: HasCustomDomain
    DependsOn: ApiDomainName
    Properties:
      DomainName: staging.api.gameflex.io
      RestApiId: !Ref ApiGateway
      Stage: v1
      BasePath: ""

  PostsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gameflex-staging-Posts
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      DeletionProtectionEnabled: true
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  MediaTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gameflex-staging-Media
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      DeletionProtectionEnabled: true
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  UserProfilesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gameflex-staging-UserProfiles
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      DeletionProtectionEnabled: true
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  CommentsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gameflex-staging-Comments
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: post_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: post-id-index
          KeySchema:
            - AttributeName: post_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST
      DeletionProtectionEnabled: true
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  LikesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gameflex-staging-Likes
      AttributeDefinitions:
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: post_id
          KeyType: HASH
        - AttributeName: user_id
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: user-id-index
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST
      DeletionProtectionEnabled: true
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  FollowsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gameflex-staging-Follows
      AttributeDefinitions:
        - AttributeName: follower_id
          AttributeType: S
        - AttributeName: following_id
          AttributeType: S
      KeySchema:
        - AttributeName: follower_id
          KeyType: HASH
        - AttributeName: following_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      DeletionProtectionEnabled: true
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  ChannelsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gameflex-staging-Channels
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: owner_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: owner-id-index
          KeySchema:
            - AttributeName: owner_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST
      DeletionProtectionEnabled: true
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  ChannelMembersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gameflex-staging-ChannelMembers
      AttributeDefinitions:
        - AttributeName: channel_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: channel_id
          KeyType: HASH
        - AttributeName: user_id
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: user-id-index
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST
      DeletionProtectionEnabled: true
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  ReflexesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gameflex-staging-Reflexes
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: post-id-index
          KeySchema:
            - AttributeName: post_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: user-id-index
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST
      DeletionProtectionEnabled: true
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  # Cognito User Pool
  UserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: gameflex-users-staging
      AutoVerifiedAttributes:
        - email
      UsernameAttributes:
        - email
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: false
      DeletionProtection: ACTIVE
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain

  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      ClientName: gameflex-client-staging
      UserPoolId: !Ref UserPool
      GenerateSecret: false
      ExplicitAuthFlows:
        - ALLOW_ADMIN_USER_PASSWORD_AUTH
        - ALLOW_USER_PASSWORD_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH
      TokenValidityUnits:
        RefreshToken: days
        AccessToken: minutes
        IdToken: minutes
      RefreshTokenValidity: 30
      AccessTokenValidity: 60
      IdTokenValidity: 60

  # Lambda Functions
  AuthorizerFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: gameflex-authorizer-staging
      CodeUri: src/authorizer/
      Handler: index.handler
      Runtime: nodejs18.x
      Environment:
        Variables:
          USER_POOL_ID: !Ref UserPool
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:GetUser
              Resource: !GetAtt UserPool.Arn

  AuthFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: gameflex-auth-staging
      CodeUri: src/auth/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:*
              Resource: !GetAtt UserPool.Arn
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:Admin*
                - cognito-idp:DescribeIdentityProvider
                - cognito-idp:DescribeResourceServer
                - cognito-idp:DescribeUserPool
                - cognito-idp:DescribeUserPoolClient
                - cognito-idp:DescribeUserPoolDomain
                - cognito-idp:GetGroup
                - cognito-idp:ListGroups
                - cognito-idp:ListUserPoolClients
                - cognito-idp:ListUsers
                - cognito-idp:ListUsersInGroup
                - cognito-idp:UpdateGroup
              Resource: !GetAtt UserPool.Arn
      Events:
        AuthSignin:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/signin
            Method: POST
        AuthSignup:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/signup
            Method: POST
        AuthRefresh:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/refresh
            Method: POST
        AuthValidate:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/validate
            Method: GET

  PostsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: gameflex-posts-staging
      CodeUri: src/posts/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref PostsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref MediaTable
        - DynamoDBCrudPolicy:
            TableName: !Ref CommentsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref LikesTable
        - DynamoDBCrudPolicy:
            TableName: !Ref ReflexesTable
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
      Events:
        GetPosts:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer
        CreateDraftPost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/draft
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        CreatePost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        GetPost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}
            Method: GET
        AttachMediaToPost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/media
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer
        PublishPost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/publish
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer
        UpdatePost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer
        DeletePost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}
            Method: DELETE
            Auth:
              Authorizer: CognitoAuthorizer
        LikePost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/like
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        UnlikePost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/like
            Method: DELETE
            Auth:
              Authorizer: CognitoAuthorizer
        GetComments:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/comments
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer
        CreateComment:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/comments
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        UpdateComment:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /comments/{id}
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer
        DeleteComment:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /comments/{id}
            Method: DELETE
            Auth:
              Authorizer: CognitoAuthorizer

  MediaFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: gameflex-media-staging
      CodeUri: src/media/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref MediaTable
        - Statement:
            - Effect: Allow
              Action:
                - secretsmanager:GetSecretValue
              Resource:
                - !Ref R2Secret
                - !Ref AppConfigSecret
              Condition:
                StringEquals:
                  "aws:RequestedRegion": !Ref AWS::Region
      Events:
        UploadMedia:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /media/upload
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        GetMedia:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /media/{id}
            Method: GET
        DeleteMedia:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /media/{id}
            Method: DELETE
            Auth:
              Authorizer: CognitoAuthorizer
        UpdateMedia:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /media/{id}
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer
